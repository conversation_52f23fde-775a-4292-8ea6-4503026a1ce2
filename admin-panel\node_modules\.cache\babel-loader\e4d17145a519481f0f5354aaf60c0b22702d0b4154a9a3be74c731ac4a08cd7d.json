{"ast": null, "code": "var _jsxFileName = \"D:\\\\project\\\\HNrealstate\\\\admin-panel\\\\src\\\\pages\\\\Properties.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Grid, Avatar, Pagination, InputAdornment, Fab, Tooltip } from '@mui/material';\nimport { Edit, Delete, Add, Search, LocationOn } from '@mui/icons-material';\nimport { fetchProperties, createProperty, updateProperty, deleteProperty, clearError } from '../store/slices/propertySlice';\nimport { fetchAgents } from '../store/slices/userSlice';\n\n// Type for property data sent to API (agent is string ID)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Properties = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    properties,\n    loading,\n    error,\n    total,\n    pagination\n  } = useSelector(state => state.properties);\n  const {\n    agents\n  } = useSelector(state => state.users);\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const [open, setOpen] = useState(false);\n  const [editingProperty, setEditingProperty] = useState(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [propertyToDelete, setPropertyToDelete] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    price: '',\n    location: '',\n    bedrooms: '',\n    bathrooms: '',\n    area: '',\n    type: 'sale',\n    status: 'available',\n    features: '',\n    agent: ''\n  });\n  useEffect(() => {\n    const params = {\n      page: currentPage,\n      limit: 10\n    };\n    if (searchTerm) {\n      params.location = searchTerm;\n    }\n    if (filterType !== 'all') {\n      params.category = filterType;\n    }\n    dispatch(fetchProperties(params));\n    dispatch(fetchAgents());\n  }, [dispatch, currentPage, searchTerm, filterType]);\n  const handleOpenDialog = property => {\n    if (property) {\n      setEditingProperty(property);\n      setFormData({\n        title: property.title,\n        description: property.description,\n        price: property.price.toString(),\n        location: property.location,\n        bedrooms: property.bedrooms.toString(),\n        bathrooms: property.bathrooms.toString(),\n        area: property.area.toString(),\n        type: property.type,\n        status: property.status,\n        features: property.features.join(', '),\n        agent: property.agent._id\n      });\n    } else {\n      setEditingProperty(null);\n      setFormData({\n        title: '',\n        description: '',\n        price: '',\n        location: '',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        type: 'sale',\n        status: 'available',\n        features: '',\n        agent: (user === null || user === void 0 ? void 0 : user._id) || ''\n      });\n    }\n    setOpen(true);\n  };\n  const handleCloseDialog = () => {\n    setOpen(false);\n    setEditingProperty(null);\n    dispatch(clearError());\n  };\n  const handleSubmit = async () => {\n    const propertyData = {\n      ...formData,\n      price: Number(formData.price),\n      bedrooms: Number(formData.bedrooms),\n      bathrooms: Number(formData.bathrooms),\n      area: Number(formData.area),\n      features: formData.features.split(',').map(f => f.trim()).filter(f => f)\n    };\n    try {\n      if (editingProperty) {\n        await dispatch(updateProperty({\n          id: editingProperty._id,\n          data: propertyData\n        })).unwrap();\n      } else {\n        await dispatch(createProperty(propertyData)).unwrap();\n      }\n      handleCloseDialog();\n    } catch (error) {\n      // Error handled by slice\n    }\n  };\n  const handleDeleteClick = property => {\n    setPropertyToDelete(property);\n    setDeleteDialogOpen(true);\n  };\n  const handleDeleteConfirm = async () => {\n    if (propertyToDelete) {\n      try {\n        await dispatch(deleteProperty(propertyToDelete._id)).unwrap();\n        setDeleteDialogOpen(false);\n        setPropertyToDelete(null);\n      } catch (error) {\n        // Error handled by slice\n      }\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'available':\n        return '#4CAF50';\n      case 'sold':\n        return '#F44336';\n      case 'rented':\n        return '#FF9800';\n      default:\n        return '#757575';\n    }\n  };\n  const getTypeColor = type => {\n    switch (type) {\n      case 'sale':\n        return '#4B0E14';\n      case 'rent':\n        return '#C5A059';\n      case 'off-plan':\n        return '#2C2C2C';\n      default:\n        return '#757575';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          sx: {\n            fontWeight: 700,\n            color: '#4B0E14',\n            mb: 1\n          },\n          children: \"Properties Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            color: '#666'\n          },\n          children: \"Manage all properties in your real estate portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            placeholder: \"Search by location...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            select: true,\n            fullWidth: true,\n            label: \"Filter by Type\",\n            value: filterType,\n            onChange: e => setFilterType(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"all\",\n              children: \"All Types\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"sale\",\n              children: \"For Sale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"rent\",\n              children: \"For Rent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"off-plan\",\n              children: \"Off-Plan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 5,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1,\n              alignItems: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#666'\n              },\n              children: [\"Total: \", total, \" properties\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#f8f5f0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Property\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Price\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Agent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: properties.map(property => /*#__PURE__*/_jsxDEV(TableRow, {\n            hover: true,\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: property.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666'\n                  },\n                  children: [property.bedrooms, \" bed \\u2022 \", property.bathrooms, \" bath \\u2022 \", property.area, \" sqm\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(LocationOn, {\n                  sx: {\n                    fontSize: 16,\n                    color: '#666',\n                    mr: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: property.location\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#C5A059'\n                },\n                children: [\"$\", property.price.toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: property.type.toUpperCase(),\n                size: \"small\",\n                sx: {\n                  backgroundColor: getTypeColor(property.type),\n                  color: 'white',\n                  fontWeight: 600\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: property.status.toUpperCase(),\n                size: \"small\",\n                sx: {\n                  backgroundColor: getStatusColor(property.status),\n                  color: 'white',\n                  fontWeight: 600\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 32,\n                    height: 32,\n                    mr: 1,\n                    backgroundColor: '#4B0E14',\n                    fontSize: '0.875rem'\n                  },\n                  children: property.agent.name.charAt(0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: property.agent.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Edit Property\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleOpenDialog(property),\n                    sx: {\n                      color: '#4B0E14'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Delete Property\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDeleteClick(property),\n                    sx: {\n                      color: '#F44336'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 367,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)]\n          }, property._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this), pagination.pages > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        count: pagination.pages,\n        page: currentPage,\n        onChange: (_, page) => setCurrentPage(page),\n        color: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Fab, {\n      color: \"primary\",\n      \"aria-label\": \"add property\",\n      sx: {\n        position: 'fixed',\n        bottom: 16,\n        right: 16,\n        backgroundColor: '#4B0E14',\n        '&:hover': {\n          backgroundColor: '#3a0b10'\n        }\n      },\n      onClick: () => handleOpenDialog(),\n      children: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: handleCloseDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingProperty ? 'Edit Property' : 'Add New Property'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Property Title\",\n              value: formData.title,\n              onChange: e => setFormData({\n                ...formData,\n                title: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Description\",\n              multiline: true,\n              rows: 3,\n              value: formData.description,\n              onChange: e => setFormData({\n                ...formData,\n                description: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Price\",\n              type: \"number\",\n              value: formData.price,\n              onChange: e => setFormData({\n                ...formData,\n                price: e.target.value\n              }),\n              required: true,\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: \"$\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 35\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Location\",\n              value: formData.location,\n              onChange: e => setFormData({\n                ...formData,\n                location: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Bedrooms\",\n              type: \"number\",\n              value: formData.bedrooms,\n              onChange: e => setFormData({\n                ...formData,\n                bedrooms: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Bathrooms\",\n              type: \"number\",\n              value: formData.bathrooms,\n              onChange: e => setFormData({\n                ...formData,\n                bathrooms: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Area (sqm)\",\n              type: \"number\",\n              value: formData.area,\n              onChange: e => setFormData({\n                ...formData,\n                area: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              fullWidth: true,\n              label: \"Type\",\n              value: formData.type,\n              onChange: e => setFormData({\n                ...formData,\n                type: e.target.value\n              }),\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"sale\",\n                children: \"For Sale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"rent\",\n                children: \"For Rent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"off-plan\",\n                children: \"Off-Plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              fullWidth: true,\n              label: \"Status\",\n              value: formData.status,\n              onChange: e => setFormData({\n                ...formData,\n                status: e.target.value\n              }),\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"available\",\n                children: \"Available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"sold\",\n                children: \"Sold\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"rented\",\n                children: \"Rented\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              fullWidth: true,\n              label: \"Agent\",\n              value: formData.agent,\n              onChange: e => setFormData({\n                ...formData,\n                agent: e.target.value\n              }),\n              required: true,\n              children: agents.map(agent => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: agent._id,\n                children: [agent.name, \" (\", agent.role, \")\"]\n              }, agent._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Features (comma separated)\",\n              value: formData.features,\n              onChange: e => setFormData({\n                ...formData,\n                features: e.target.value\n              }),\n              placeholder: \"e.g. Swimming Pool, Gym, Parking, Garden\",\n              helperText: \"Enter features separated by commas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          disabled: loading,\n          sx: {\n            backgroundColor: '#4B0E14',\n            '&:hover': {\n              backgroundColor: '#3a0b10'\n            }\n          },\n          children: [editingProperty ? 'Update' : 'Create', \" Property\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: () => setDeleteDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Confirm Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Are you sure you want to delete \\\"\", propertyToDelete === null || propertyToDelete === void 0 ? void 0 : propertyToDelete.title, \"\\\"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteConfirm,\n          color: \"error\",\n          variant: \"contained\",\n          disabled: loading,\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(Properties, \"HbcE7ay+tAH/WAlN+ldsd/uoCQk=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector];\n});\n_c = Properties;\nexport default Properties;\nvar _c;\n$RefreshReg$(_c, \"Properties\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "Grid", "Avatar", "Pagination", "InputAdornment", "Fab", "<PERSON><PERSON><PERSON>", "Edit", "Delete", "Add", "Search", "LocationOn", "fetchProperties", "createProperty", "updateProperty", "deleteProperty", "clearError", "fetchAgents", "jsxDEV", "_jsxDEV", "Properties", "_s", "dispatch", "properties", "loading", "error", "total", "pagination", "state", "agents", "users", "user", "auth", "open", "<PERSON><PERSON><PERSON>", "editingProperty", "setEditingProperty", "deleteDialogOpen", "setDeleteDialogOpen", "propertyToDelete", "setPropertyToDelete", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "currentPage", "setCurrentPage", "formData", "setFormData", "title", "description", "price", "location", "bedrooms", "bathrooms", "area", "type", "status", "features", "agent", "params", "page", "limit", "category", "handleOpenDialog", "property", "toString", "join", "_id", "handleCloseDialog", "handleSubmit", "propertyData", "Number", "split", "map", "f", "trim", "filter", "id", "data", "unwrap", "handleDeleteClick", "handleDeleteConfirm", "getStatusColor", "getTypeColor", "sx", "p", "children", "display", "justifyContent", "alignItems", "mb", "variant", "component", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "item", "xs", "md", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "position", "select", "label", "gap", "backgroundColor", "hover", "fontSize", "mr", "toLocaleString", "toUpperCase", "size", "width", "height", "name", "char<PERSON>t", "onClick", "pages", "mt", "count", "_", "bottom", "right", "onClose", "max<PERSON><PERSON><PERSON>", "required", "multiline", "rows", "role", "helperText", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/project/HNrealstate/admin-panel/src/pages/Properties.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Grid,\n  Avatar,\n  Pagination,\n  InputAdornment,\n  Fab,\n  Tooltip\n} from '@mui/material';\nimport {\n  Edit,\n  Delete,\n  Add,\n  Search,\n  FilterList,\n  Home,\n  LocationOn,\n  AttachMoney\n} from '@mui/icons-material';\nimport { RootState, AppDispatch } from '../store/store';\nimport {\n  fetchProperties,\n  createProperty,\n  updateProperty,\n  deleteProperty,\n  clearError,\n  Property\n} from '../store/slices/propertySlice';\nimport { fetchAgents } from '../store/slices/userSlice';\n\n// Type for property data sent to API (agent is string ID)\ntype PropertyFormData = Omit<Property, '_id' | 'agent' | 'createdAt' | 'updatedAt' | 'images'> & {\n  agent: string;\n  images?: string[];\n};\n\nconst Properties: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { properties, loading, error, total, pagination } = useSelector(\n    (state: RootState) => state.properties\n  );\n  const { agents } = useSelector((state: RootState) => state.users);\n  const { user } = useSelector((state: RootState) => state.auth);\n\n  const [open, setOpen] = useState(false);\n  const [editingProperty, setEditingProperty] = useState<Property | null>(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [propertyToDelete, setPropertyToDelete] = useState<Property | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    price: '',\n    location: '',\n    bedrooms: '',\n    bathrooms: '',\n    area: '',\n    type: 'sale' as 'sale' | 'rent' | 'off-plan',\n    status: 'available' as 'available' | 'sold' | 'rented',\n    features: '',\n    agent: ''\n  });\n\n  useEffect(() => {\n    const params: any = {\n      page: currentPage,\n      limit: 10\n    };\n    \n    if (searchTerm) {\n      params.location = searchTerm;\n    }\n    \n    if (filterType !== 'all') {\n      params.category = filterType;\n    }\n\n    dispatch(fetchProperties(params));\n    dispatch(fetchAgents());\n  }, [dispatch, currentPage, searchTerm, filterType]);\n\n  const handleOpenDialog = (property?: Property) => {\n    if (property) {\n      setEditingProperty(property);\n      setFormData({\n        title: property.title,\n        description: property.description,\n        price: property.price.toString(),\n        location: property.location,\n        bedrooms: property.bedrooms.toString(),\n        bathrooms: property.bathrooms.toString(),\n        area: property.area.toString(),\n        type: property.type,\n        status: property.status,\n        features: property.features.join(', '),\n        agent: property.agent._id\n      });\n    } else {\n      setEditingProperty(null);\n      setFormData({\n        title: '',\n        description: '',\n        price: '',\n        location: '',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        type: 'sale',\n        status: 'available',\n        features: '',\n        agent: user?._id || ''\n      });\n    }\n    setOpen(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpen(false);\n    setEditingProperty(null);\n    dispatch(clearError());\n  };\n\n  const handleSubmit = async () => {\n    const propertyData: PropertyFormData = {\n      ...formData,\n      price: Number(formData.price),\n      bedrooms: Number(formData.bedrooms),\n      bathrooms: Number(formData.bathrooms),\n      area: Number(formData.area),\n      features: formData.features.split(',').map(f => f.trim()).filter(f => f)\n    };\n\n    try {\n      if (editingProperty) {\n        await dispatch(updateProperty({ id: editingProperty._id, data: propertyData as any })).unwrap();\n      } else {\n        await dispatch(createProperty(propertyData as any)).unwrap();\n      }\n      handleCloseDialog();\n    } catch (error) {\n      // Error handled by slice\n    }\n  };\n\n  const handleDeleteClick = (property: Property) => {\n    setPropertyToDelete(property);\n    setDeleteDialogOpen(true);\n  };\n\n  const handleDeleteConfirm = async () => {\n    if (propertyToDelete) {\n      try {\n        await dispatch(deleteProperty(propertyToDelete._id)).unwrap();\n        setDeleteDialogOpen(false);\n        setPropertyToDelete(null);\n      } catch (error) {\n        // Error handled by slice\n      }\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'available':\n        return '#4CAF50';\n      case 'sold':\n        return '#F44336';\n      case 'rented':\n        return '#FF9800';\n      default:\n        return '#757575';\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'sale':\n        return '#4B0E14';\n      case 'rent':\n        return '#C5A059';\n      case 'off-plan':\n        return '#2C2C2C';\n      default:\n        return '#757575';\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box>\n          <Typography\n            variant=\"h4\"\n            component=\"h1\"\n            sx={{\n              fontWeight: 700,\n              color: '#4B0E14',\n              mb: 1\n            }}\n          >\n            Properties Management\n          </Typography>\n          <Typography variant=\"body1\" sx={{ color: '#666' }}>\n            Manage all properties in your real estate portfolio\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Filters and Search */}\n      <Paper sx={{ p: 2, mb: 3 }}>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={12} md={4}>\n            <TextField\n              fullWidth\n              placeholder=\"Search by location...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Search />\n                  </InputAdornment>\n                ),\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <TextField\n              select\n              fullWidth\n              label=\"Filter by Type\"\n              value={filterType}\n              onChange={(e) => setFilterType(e.target.value)}\n            >\n              <MenuItem value=\"all\">All Types</MenuItem>\n              <MenuItem value=\"sale\">For Sale</MenuItem>\n              <MenuItem value=\"rent\">For Rent</MenuItem>\n              <MenuItem value=\"off-plan\">Off-Plan</MenuItem>\n            </TextField>\n          </Grid>\n          <Grid item xs={12} md={5}>\n            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>\n              <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                Total: {total} properties\n              </Typography>\n            </Box>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Properties Table */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow sx={{ backgroundColor: '#f8f5f0' }}>\n              <TableCell sx={{ fontWeight: 600 }}>Property</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Location</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Price</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Type</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Agent</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {properties.map((property) => (\n              <TableRow key={property._id} hover>\n                <TableCell>\n                  <Box>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 600 }}>\n                      {property.title}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      {property.bedrooms} bed • {property.bathrooms} bath • {property.area} sqm\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <LocationOn sx={{ fontSize: 16, color: '#666', mr: 0.5 }} />\n                    <Typography variant=\"body2\">{property.location}</Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: '#C5A059' }}>\n                    ${property.price.toLocaleString()}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={property.type.toUpperCase()}\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: getTypeColor(property.type),\n                      color: 'white',\n                      fontWeight: 600\n                    }}\n                  />\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={property.status.toUpperCase()}\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: getStatusColor(property.status),\n                      color: 'white',\n                      fontWeight: 600\n                    }}\n                  />\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Avatar\n                      sx={{\n                        width: 32,\n                        height: 32,\n                        mr: 1,\n                        backgroundColor: '#4B0E14',\n                        fontSize: '0.875rem'\n                      }}\n                    >\n                      {property.agent.name.charAt(0)}\n                    </Avatar>\n                    <Typography variant=\"body2\">{property.agent.name}</Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <Tooltip title=\"Edit Property\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleOpenDialog(property)}\n                        sx={{ color: '#4B0E14' }}\n                      >\n                        <Edit />\n                      </IconButton>\n                    </Tooltip>\n                    <Tooltip title=\"Delete Property\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleDeleteClick(property)}\n                        sx={{ color: '#F44336' }}\n                      >\n                        <Delete />\n                      </IconButton>\n                    </Tooltip>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Pagination */}\n      {pagination.pages > 1 && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>\n          <Pagination\n            count={pagination.pages}\n            page={currentPage}\n            onChange={(_, page) => setCurrentPage(page)}\n            color=\"primary\"\n          />\n        </Box>\n      )}\n\n      {/* Floating Action Button */}\n      <Fab\n        color=\"primary\"\n        aria-label=\"add property\"\n        sx={{\n          position: 'fixed',\n          bottom: 16,\n          right: 16,\n          backgroundColor: '#4B0E14',\n          '&:hover': {\n            backgroundColor: '#3a0b10'\n          }\n        }}\n        onClick={() => handleOpenDialog()}\n      >\n        <Add />\n      </Fab>\n\n      {/* Property Dialog */}\n      <Dialog open={open} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingProperty ? 'Edit Property' : 'Add New Property'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Property Title\"\n                value={formData.title}\n                onChange={(e) => setFormData({ ...formData, title: e.target.value })}\n                required\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Description\"\n                multiline\n                rows={3}\n                value={formData.description}\n                onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                required\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Price\"\n                type=\"number\"\n                value={formData.price}\n                onChange={(e) => setFormData({ ...formData, price: e.target.value })}\n                required\n                InputProps={{\n                  startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Location\"\n                value={formData.location}\n                onChange={(e) => setFormData({ ...formData, location: e.target.value })}\n                required\n              />\n            </Grid>\n            <Grid item xs={6} md={3}>\n              <TextField\n                fullWidth\n                label=\"Bedrooms\"\n                type=\"number\"\n                value={formData.bedrooms}\n                onChange={(e) => setFormData({ ...formData, bedrooms: e.target.value })}\n                required\n              />\n            </Grid>\n            <Grid item xs={6} md={3}>\n              <TextField\n                fullWidth\n                label=\"Bathrooms\"\n                type=\"number\"\n                value={formData.bathrooms}\n                onChange={(e) => setFormData({ ...formData, bathrooms: e.target.value })}\n                required\n              />\n            </Grid>\n            <Grid item xs={6} md={3}>\n              <TextField\n                fullWidth\n                label=\"Area (sqm)\"\n                type=\"number\"\n                value={formData.area}\n                onChange={(e) => setFormData({ ...formData, area: e.target.value })}\n                required\n              />\n            </Grid>\n            <Grid item xs={6} md={3}>\n              <TextField\n                select\n                fullWidth\n                label=\"Type\"\n                value={formData.type}\n                onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}\n                required\n              >\n                <MenuItem value=\"sale\">For Sale</MenuItem>\n                <MenuItem value=\"rent\">For Rent</MenuItem>\n                <MenuItem value=\"off-plan\">Off-Plan</MenuItem>\n              </TextField>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                select\n                fullWidth\n                label=\"Status\"\n                value={formData.status}\n                onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}\n                required\n              >\n                <MenuItem value=\"available\">Available</MenuItem>\n                <MenuItem value=\"sold\">Sold</MenuItem>\n                <MenuItem value=\"rented\">Rented</MenuItem>\n              </TextField>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                select\n                fullWidth\n                label=\"Agent\"\n                value={formData.agent}\n                onChange={(e) => setFormData({ ...formData, agent: e.target.value })}\n                required\n              >\n                {agents.map((agent) => (\n                  <MenuItem key={agent._id} value={agent._id}>\n                    {agent.name} ({agent.role})\n                  </MenuItem>\n                ))}\n              </TextField>\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Features (comma separated)\"\n                value={formData.features}\n                onChange={(e) => setFormData({ ...formData, features: e.target.value })}\n                placeholder=\"e.g. Swimming Pool, Gym, Parking, Garden\"\n                helperText=\"Enter features separated by commas\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Cancel</Button>\n          <Button\n            onClick={handleSubmit}\n            variant=\"contained\"\n            disabled={loading}\n            sx={{\n              backgroundColor: '#4B0E14',\n              '&:hover': {\n                backgroundColor: '#3a0b10'\n              }\n            }}\n          >\n            {editingProperty ? 'Update' : 'Create'} Property\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>\n        <DialogTitle>Confirm Delete</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{propertyToDelete?.title}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button\n            onClick={handleDeleteConfirm}\n            color=\"error\"\n            variant=\"contained\"\n            disabled={loading}\n          >\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Properties;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,cAAc,EACdC,GAAG,EACHC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,EACJC,MAAM,EACNC,GAAG,EACHC,MAAM,EAGNC,UAAU,QAEL,qBAAqB;AAE5B,SACEC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,UAAU,QAEL,+BAA+B;AACtC,SAASC,WAAW,QAAQ,2BAA2B;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAMA,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGzC,WAAW,CAAc,CAAC;EAC3C,MAAM;IAAE0C,UAAU;IAAEC,OAAO;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAG7C,WAAW,CAClE8C,KAAgB,IAAKA,KAAK,CAACL,UAC9B,CAAC;EACD,MAAM;IAAEM;EAAO,CAAC,GAAG/C,WAAW,CAAE8C,KAAgB,IAAKA,KAAK,CAACE,KAAK,CAAC;EACjE,MAAM;IAAEC;EAAK,CAAC,GAAGjD,WAAW,CAAE8C,KAAgB,IAAKA,KAAK,CAACI,IAAI,CAAC;EAE9D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAkB,IAAI,CAAC;EAC7E,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAkB,IAAI,CAAC;EAC/E,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAM,CAACmE,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC;IACvCqE,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,MAAsC;IAC5CC,MAAM,EAAE,WAA8C;IACtDC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFhF,SAAS,CAAC,MAAM;IACd,MAAMiF,MAAW,GAAG;MAClBC,IAAI,EAAEhB,WAAW;MACjBiB,KAAK,EAAE;IACT,CAAC;IAED,IAAIrB,UAAU,EAAE;MACdmB,MAAM,CAACR,QAAQ,GAAGX,UAAU;IAC9B;IAEA,IAAIE,UAAU,KAAK,KAAK,EAAE;MACxBiB,MAAM,CAACG,QAAQ,GAAGpB,UAAU;IAC9B;IAEArB,QAAQ,CAACV,eAAe,CAACgD,MAAM,CAAC,CAAC;IACjCtC,QAAQ,CAACL,WAAW,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAACK,QAAQ,EAAEuB,WAAW,EAAEJ,UAAU,EAAEE,UAAU,CAAC,CAAC;EAEnD,MAAMqB,gBAAgB,GAAIC,QAAmB,IAAK;IAChD,IAAIA,QAAQ,EAAE;MACZ7B,kBAAkB,CAAC6B,QAAQ,CAAC;MAC5BjB,WAAW,CAAC;QACVC,KAAK,EAAEgB,QAAQ,CAAChB,KAAK;QACrBC,WAAW,EAAEe,QAAQ,CAACf,WAAW;QACjCC,KAAK,EAAEc,QAAQ,CAACd,KAAK,CAACe,QAAQ,CAAC,CAAC;QAChCd,QAAQ,EAAEa,QAAQ,CAACb,QAAQ;QAC3BC,QAAQ,EAAEY,QAAQ,CAACZ,QAAQ,CAACa,QAAQ,CAAC,CAAC;QACtCZ,SAAS,EAAEW,QAAQ,CAACX,SAAS,CAACY,QAAQ,CAAC,CAAC;QACxCX,IAAI,EAAEU,QAAQ,CAACV,IAAI,CAACW,QAAQ,CAAC,CAAC;QAC9BV,IAAI,EAAES,QAAQ,CAACT,IAAI;QACnBC,MAAM,EAAEQ,QAAQ,CAACR,MAAM;QACvBC,QAAQ,EAAEO,QAAQ,CAACP,QAAQ,CAACS,IAAI,CAAC,IAAI,CAAC;QACtCR,KAAK,EAAEM,QAAQ,CAACN,KAAK,CAACS;MACxB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLhC,kBAAkB,CAAC,IAAI,CAAC;MACxBY,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,WAAW;QACnBC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC,GAAG,KAAI;MACtB,CAAC,CAAC;IACJ;IACAlC,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnC,OAAO,CAAC,KAAK,CAAC;IACdE,kBAAkB,CAAC,IAAI,CAAC;IACxBd,QAAQ,CAACN,UAAU,CAAC,CAAC,CAAC;EACxB,CAAC;EAED,MAAMsD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMC,YAA8B,GAAG;MACrC,GAAGxB,QAAQ;MACXI,KAAK,EAAEqB,MAAM,CAACzB,QAAQ,CAACI,KAAK,CAAC;MAC7BE,QAAQ,EAAEmB,MAAM,CAACzB,QAAQ,CAACM,QAAQ,CAAC;MACnCC,SAAS,EAAEkB,MAAM,CAACzB,QAAQ,CAACO,SAAS,CAAC;MACrCC,IAAI,EAAEiB,MAAM,CAACzB,QAAQ,CAACQ,IAAI,CAAC;MAC3BG,QAAQ,EAAEX,QAAQ,CAACW,QAAQ,CAACe,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACF,CAAC,IAAIA,CAAC;IACzE,CAAC;IAED,IAAI;MACF,IAAIxC,eAAe,EAAE;QACnB,MAAMb,QAAQ,CAACR,cAAc,CAAC;UAAEgE,EAAE,EAAE3C,eAAe,CAACiC,GAAG;UAAEW,IAAI,EAAER;QAAoB,CAAC,CAAC,CAAC,CAACS,MAAM,CAAC,CAAC;MACjG,CAAC,MAAM;QACL,MAAM1D,QAAQ,CAACT,cAAc,CAAC0D,YAAmB,CAAC,CAAC,CAACS,MAAM,CAAC,CAAC;MAC9D;MACAX,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACd;IAAA;EAEJ,CAAC;EAED,MAAMwD,iBAAiB,GAAIhB,QAAkB,IAAK;IAChDzB,mBAAmB,CAACyB,QAAQ,CAAC;IAC7B3B,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM4C,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI3C,gBAAgB,EAAE;MACpB,IAAI;QACF,MAAMjB,QAAQ,CAACP,cAAc,CAACwB,gBAAgB,CAAC6B,GAAG,CAAC,CAAC,CAACY,MAAM,CAAC,CAAC;QAC7D1C,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,CAAC,OAAOf,KAAK,EAAE;QACd;MAAA;IAEJ;EACF,CAAC;EAED,MAAM0D,cAAc,GAAI1B,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,MAAM;QACT,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAM2B,YAAY,GAAI5B,IAAY,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,MAAM;QACT,OAAO,SAAS;MAClB,KAAK,MAAM;QACT,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACErC,OAAA,CAACpC,GAAG;IAACsG,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhBpE,OAAA,CAACpC,GAAG;MAACsG,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACzFpE,OAAA,CAACpC,GAAG;QAAAwG,QAAA,gBACFpE,OAAA,CAACnC,UAAU;UACT4G,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdR,EAAE,EAAE;YACFS,UAAU,EAAE,GAAG;YACfC,KAAK,EAAE,SAAS;YAChBJ,EAAE,EAAE;UACN,CAAE;UAAAJ,QAAA,EACH;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhF,OAAA,CAACnC,UAAU;UAAC4G,OAAO,EAAC,OAAO;UAACP,EAAE,EAAE;YAAEU,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAEnD;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA,CAACjC,KAAK;MAACmG,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACzBpE,OAAA,CAAClB,IAAI;QAACmG,SAAS;QAACC,OAAO,EAAE,CAAE;QAACX,UAAU,EAAC,QAAQ;QAAAH,QAAA,gBAC7CpE,OAAA,CAAClB,IAAI;UAACqG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAjB,QAAA,eACvBpE,OAAA,CAACpB,SAAS;YACR0G,SAAS;YACTC,WAAW,EAAC,uBAAuB;YACnCC,KAAK,EAAElE,UAAW;YAClBmE,QAAQ,EAAGC,CAAC,IAAKnE,aAAa,CAACmE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CI,UAAU,EAAE;cACVC,cAAc,eACZ7F,OAAA,CAACf,cAAc;gBAAC6G,QAAQ,EAAC,OAAO;gBAAA1B,QAAA,eAC9BpE,OAAA,CAACT,MAAM;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPhF,OAAA,CAAClB,IAAI;UAACqG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAjB,QAAA,eACvBpE,OAAA,CAACpB,SAAS;YACRmH,MAAM;YACNT,SAAS;YACTU,KAAK,EAAC,gBAAgB;YACtBR,KAAK,EAAEhE,UAAW;YAClBiE,QAAQ,EAAGC,CAAC,IAAKjE,aAAa,CAACiE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAApB,QAAA,gBAE/CpE,OAAA,CAACnB,QAAQ;cAAC2G,KAAK,EAAC,KAAK;cAAApB,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1ChF,OAAA,CAACnB,QAAQ;cAAC2G,KAAK,EAAC,MAAM;cAAApB,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1ChF,OAAA,CAACnB,QAAQ;cAAC2G,KAAK,EAAC,MAAM;cAAApB,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1ChF,OAAA,CAACnB,QAAQ;cAAC2G,KAAK,EAAC,UAAU;cAAApB,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACPhF,OAAA,CAAClB,IAAI;UAACqG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAjB,QAAA,eACvBpE,OAAA,CAACpC,GAAG;YAACsG,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAE4B,GAAG,EAAE,CAAC;cAAE1B,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,eACzDpE,OAAA,CAACnC,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAACP,EAAE,EAAE;gBAAEU,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,GAAC,SAC1C,EAAC7D,KAAK,EAAC,aAChB;YAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRhF,OAAA,CAAC7B,cAAc;MAACuG,SAAS,EAAE3G,KAAM;MAAAqG,QAAA,eAC/BpE,OAAA,CAAChC,KAAK;QAAAoG,QAAA,gBACJpE,OAAA,CAAC5B,SAAS;UAAAgG,QAAA,eACRpE,OAAA,CAAC3B,QAAQ;YAAC6F,EAAE,EAAE;cAAEgC,eAAe,EAAE;YAAU,CAAE;YAAA9B,QAAA,gBAC3CpE,OAAA,CAAC9B,SAAS;cAACgG,EAAE,EAAE;gBAAES,UAAU,EAAE;cAAI,CAAE;cAAAP,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxDhF,OAAA,CAAC9B,SAAS;cAACgG,EAAE,EAAE;gBAAES,UAAU,EAAE;cAAI,CAAE;cAAAP,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxDhF,OAAA,CAAC9B,SAAS;cAACgG,EAAE,EAAE;gBAAES,UAAU,EAAE;cAAI,CAAE;cAAAP,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrDhF,OAAA,CAAC9B,SAAS;cAACgG,EAAE,EAAE;gBAAES,UAAU,EAAE;cAAI,CAAE;cAAAP,QAAA,EAAC;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpDhF,OAAA,CAAC9B,SAAS;cAACgG,EAAE,EAAE;gBAAES,UAAU,EAAE;cAAI,CAAE;cAAAP,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtDhF,OAAA,CAAC9B,SAAS;cAACgG,EAAE,EAAE;gBAAES,UAAU,EAAE;cAAI,CAAE;cAAAP,QAAA,EAAC;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrDhF,OAAA,CAAC9B,SAAS;cAACgG,EAAE,EAAE;gBAAES,UAAU,EAAE;cAAI,CAAE;cAAAP,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZhF,OAAA,CAAC/B,SAAS;UAAAmG,QAAA,EACPhE,UAAU,CAACmD,GAAG,CAAET,QAAQ,iBACvB9C,OAAA,CAAC3B,QAAQ;YAAoB8H,KAAK;YAAA/B,QAAA,gBAChCpE,OAAA,CAAC9B,SAAS;cAAAkG,QAAA,eACRpE,OAAA,CAACpC,GAAG;gBAAAwG,QAAA,gBACFpE,OAAA,CAACnC,UAAU;kBAAC4G,OAAO,EAAC,WAAW;kBAACP,EAAE,EAAE;oBAAES,UAAU,EAAE;kBAAI,CAAE;kBAAAP,QAAA,EACrDtB,QAAQ,CAAChB;gBAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACbhF,OAAA,CAACnC,UAAU;kBAAC4G,OAAO,EAAC,OAAO;kBAACP,EAAE,EAAE;oBAAEU,KAAK,EAAE;kBAAO,CAAE;kBAAAR,QAAA,GAC/CtB,QAAQ,CAACZ,QAAQ,EAAC,cAAO,EAACY,QAAQ,CAACX,SAAS,EAAC,eAAQ,EAACW,QAAQ,CAACV,IAAI,EAAC,MACvE;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZhF,OAAA,CAAC9B,SAAS;cAAAkG,QAAA,eACRpE,OAAA,CAACpC,GAAG;gBAACsG,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjDpE,OAAA,CAACR,UAAU;kBAAC0E,EAAE,EAAE;oBAAEkC,QAAQ,EAAE,EAAE;oBAAExB,KAAK,EAAE,MAAM;oBAAEyB,EAAE,EAAE;kBAAI;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5DhF,OAAA,CAACnC,UAAU;kBAAC4G,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAEtB,QAAQ,CAACb;gBAAQ;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZhF,OAAA,CAAC9B,SAAS;cAAAkG,QAAA,eACRpE,OAAA,CAACnC,UAAU;gBAAC4G,OAAO,EAAC,WAAW;gBAACP,EAAE,EAAE;kBAAES,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAR,QAAA,GAAC,GACxE,EAACtB,QAAQ,CAACd,KAAK,CAACsE,cAAc,CAAC,CAAC;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZhF,OAAA,CAAC9B,SAAS;cAAAkG,QAAA,eACRpE,OAAA,CAAC1B,IAAI;gBACH0H,KAAK,EAAElD,QAAQ,CAACT,IAAI,CAACkE,WAAW,CAAC,CAAE;gBACnCC,IAAI,EAAC,OAAO;gBACZtC,EAAE,EAAE;kBACFgC,eAAe,EAAEjC,YAAY,CAACnB,QAAQ,CAACT,IAAI,CAAC;kBAC5CuC,KAAK,EAAE,OAAO;kBACdD,UAAU,EAAE;gBACd;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZhF,OAAA,CAAC9B,SAAS;cAAAkG,QAAA,eACRpE,OAAA,CAAC1B,IAAI;gBACH0H,KAAK,EAAElD,QAAQ,CAACR,MAAM,CAACiE,WAAW,CAAC,CAAE;gBACrCC,IAAI,EAAC,OAAO;gBACZtC,EAAE,EAAE;kBACFgC,eAAe,EAAElC,cAAc,CAAClB,QAAQ,CAACR,MAAM,CAAC;kBAChDsC,KAAK,EAAE,OAAO;kBACdD,UAAU,EAAE;gBACd;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZhF,OAAA,CAAC9B,SAAS;cAAAkG,QAAA,eACRpE,OAAA,CAACpC,GAAG;gBAACsG,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjDpE,OAAA,CAACjB,MAAM;kBACLmF,EAAE,EAAE;oBACFuC,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVL,EAAE,EAAE,CAAC;oBACLH,eAAe,EAAE,SAAS;oBAC1BE,QAAQ,EAAE;kBACZ,CAAE;kBAAAhC,QAAA,EAEDtB,QAAQ,CAACN,KAAK,CAACmE,IAAI,CAACC,MAAM,CAAC,CAAC;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACThF,OAAA,CAACnC,UAAU;kBAAC4G,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAEtB,QAAQ,CAACN,KAAK,CAACmE;gBAAI;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZhF,OAAA,CAAC9B,SAAS;cAAAkG,QAAA,eACRpE,OAAA,CAACpC,GAAG;gBAACsG,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAE4B,GAAG,EAAE;gBAAE,CAAE;gBAAA7B,QAAA,gBACnCpE,OAAA,CAACb,OAAO;kBAAC2C,KAAK,EAAC,eAAe;kBAAAsC,QAAA,eAC5BpE,OAAA,CAACzB,UAAU;oBACTiI,IAAI,EAAC,OAAO;oBACZK,OAAO,EAAEA,CAAA,KAAMhE,gBAAgB,CAACC,QAAQ,CAAE;oBAC1CoB,EAAE,EAAE;sBAAEU,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,eAEzBpE,OAAA,CAACZ,IAAI;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACVhF,OAAA,CAACb,OAAO;kBAAC2C,KAAK,EAAC,iBAAiB;kBAAAsC,QAAA,eAC9BpE,OAAA,CAACzB,UAAU;oBACTiI,IAAI,EAAC,OAAO;oBACZK,OAAO,EAAEA,CAAA,KAAM/C,iBAAiB,CAAChB,QAAQ,CAAE;oBAC3CoB,EAAE,EAAE;sBAAEU,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,eAEzBpE,OAAA,CAACX,MAAM;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GAjFClC,QAAQ,CAACG,GAAG;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkFjB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAGhBxE,UAAU,CAACsG,KAAK,GAAG,CAAC,iBACnB9G,OAAA,CAACpC,GAAG;MAACsG,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEyC,EAAE,EAAE;MAAE,CAAE;MAAA3C,QAAA,eAC5DpE,OAAA,CAAChB,UAAU;QACTgI,KAAK,EAAExG,UAAU,CAACsG,KAAM;QACxBpE,IAAI,EAAEhB,WAAY;QAClB+D,QAAQ,EAAEA,CAACwB,CAAC,EAAEvE,IAAI,KAAKf,cAAc,CAACe,IAAI,CAAE;QAC5CkC,KAAK,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDhF,OAAA,CAACd,GAAG;MACF0F,KAAK,EAAC,SAAS;MACf,cAAW,cAAc;MACzBV,EAAE,EAAE;QACF4B,QAAQ,EAAE,OAAO;QACjBoB,MAAM,EAAE,EAAE;QACVC,KAAK,EAAE,EAAE;QACTjB,eAAe,EAAE,SAAS;QAC1B,SAAS,EAAE;UACTA,eAAe,EAAE;QACnB;MACF,CAAE;MACFW,OAAO,EAAEA,CAAA,KAAMhE,gBAAgB,CAAC,CAAE;MAAAuB,QAAA,eAElCpE,OAAA,CAACV,GAAG;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNhF,OAAA,CAACxB,MAAM;MAACsC,IAAI,EAAEA,IAAK;MAACsG,OAAO,EAAElE,iBAAkB;MAACmE,QAAQ,EAAC,IAAI;MAAC/B,SAAS;MAAAlB,QAAA,gBACrEpE,OAAA,CAACvB,WAAW;QAAA2F,QAAA,EACTpD,eAAe,GAAG,eAAe,GAAG;MAAkB;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACdhF,OAAA,CAACtB,aAAa;QAAA0F,QAAA,eACZpE,OAAA,CAAClB,IAAI;UAACmG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAChB,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAA3C,QAAA,gBACxCpE,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAChBpE,OAAA,CAACpB,SAAS;cACR0G,SAAS;cACTU,KAAK,EAAC,gBAAgB;cACtBR,KAAK,EAAE5D,QAAQ,CAACE,KAAM;cACtB2D,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,KAAK,EAAE4D,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACrE8B,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAChBpE,OAAA,CAACpB,SAAS;cACR0G,SAAS;cACTU,KAAK,EAAC,aAAa;cACnBuB,SAAS;cACTC,IAAI,EAAE,CAAE;cACRhC,KAAK,EAAE5D,QAAQ,CAACG,WAAY;cAC5B0D,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE2D,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3E8B,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvBpE,OAAA,CAACpB,SAAS;cACR0G,SAAS;cACTU,KAAK,EAAC,OAAO;cACb3D,IAAI,EAAC,QAAQ;cACbmD,KAAK,EAAE5D,QAAQ,CAACI,KAAM;cACtByD,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,KAAK,EAAE0D,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACrE8B,QAAQ;cACR1B,UAAU,EAAE;gBACVC,cAAc,eAAE7F,OAAA,CAACf,cAAc;kBAAC6G,QAAQ,EAAC,OAAO;kBAAA1B,QAAA,EAAC;gBAAC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB;cACpE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvBpE,OAAA,CAACpB,SAAS;cACR0G,SAAS;cACTU,KAAK,EAAC,UAAU;cAChBR,KAAK,EAAE5D,QAAQ,CAACK,QAAS;cACzBwD,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,QAAQ,EAAEyD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxE8B,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACtBpE,OAAA,CAACpB,SAAS;cACR0G,SAAS;cACTU,KAAK,EAAC,UAAU;cAChB3D,IAAI,EAAC,QAAQ;cACbmD,KAAK,EAAE5D,QAAQ,CAACM,QAAS;cACzBuD,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEM,QAAQ,EAAEwD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxE8B,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACtBpE,OAAA,CAACpB,SAAS;cACR0G,SAAS;cACTU,KAAK,EAAC,WAAW;cACjB3D,IAAI,EAAC,QAAQ;cACbmD,KAAK,EAAE5D,QAAQ,CAACO,SAAU;cAC1BsD,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEO,SAAS,EAAEuD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACzE8B,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACtBpE,OAAA,CAACpB,SAAS;cACR0G,SAAS;cACTU,KAAK,EAAC,YAAY;cAClB3D,IAAI,EAAC,QAAQ;cACbmD,KAAK,EAAE5D,QAAQ,CAACQ,IAAK;cACrBqD,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEQ,IAAI,EAAEsD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACpE8B,QAAQ;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACtBpE,OAAA,CAACpB,SAAS;cACRmH,MAAM;cACNT,SAAS;cACTU,KAAK,EAAC,MAAM;cACZR,KAAK,EAAE5D,QAAQ,CAACS,IAAK;cACrBoD,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAES,IAAI,EAAEqD,CAAC,CAACC,MAAM,CAACH;cAAa,CAAC,CAAE;cAC3E8B,QAAQ;cAAAlD,QAAA,gBAERpE,OAAA,CAACnB,QAAQ;gBAAC2G,KAAK,EAAC,MAAM;gBAAApB,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1ChF,OAAA,CAACnB,QAAQ;gBAAC2G,KAAK,EAAC,MAAM;gBAAApB,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1ChF,OAAA,CAACnB,QAAQ;gBAAC2G,KAAK,EAAC,UAAU;gBAAApB,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvBpE,OAAA,CAACpB,SAAS;cACRmH,MAAM;cACNT,SAAS;cACTU,KAAK,EAAC,QAAQ;cACdR,KAAK,EAAE5D,QAAQ,CAACU,MAAO;cACvBmD,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEU,MAAM,EAAEoD,CAAC,CAACC,MAAM,CAACH;cAAa,CAAC,CAAE;cAC7E8B,QAAQ;cAAAlD,QAAA,gBAERpE,OAAA,CAACnB,QAAQ;gBAAC2G,KAAK,EAAC,WAAW;gBAAApB,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAChDhF,OAAA,CAACnB,QAAQ;gBAAC2G,KAAK,EAAC,MAAM;gBAAApB,QAAA,EAAC;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtChF,OAAA,CAACnB,QAAQ;gBAAC2G,KAAK,EAAC,QAAQ;gBAAApB,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvBpE,OAAA,CAACpB,SAAS;cACRmH,MAAM;cACNT,SAAS;cACTU,KAAK,EAAC,OAAO;cACbR,KAAK,EAAE5D,QAAQ,CAACY,KAAM;cACtBiD,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEY,KAAK,EAAEkD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACrE8B,QAAQ;cAAAlD,QAAA,EAEP1D,MAAM,CAAC6C,GAAG,CAAEf,KAAK,iBAChBxC,OAAA,CAACnB,QAAQ;gBAAiB2G,KAAK,EAAEhD,KAAK,CAACS,GAAI;gBAAAmB,QAAA,GACxC5B,KAAK,CAACmE,IAAI,EAAC,IAAE,EAACnE,KAAK,CAACiF,IAAI,EAAC,GAC5B;cAAA,GAFejF,KAAK,CAACS,GAAG;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACPhF,OAAA,CAAClB,IAAI;YAACqG,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAChBpE,OAAA,CAACpB,SAAS;cACR0G,SAAS;cACTU,KAAK,EAAC,4BAA4B;cAClCR,KAAK,EAAE5D,QAAQ,CAACW,QAAS;cACzBkD,QAAQ,EAAGC,CAAC,IAAK7D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEW,QAAQ,EAAEmD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxED,WAAW,EAAC,0CAA0C;cACtDmC,UAAU,EAAC;YAAoC;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBhF,OAAA,CAACrB,aAAa;QAAAyF,QAAA,gBACZpE,OAAA,CAAClC,MAAM;UAAC+I,OAAO,EAAE3D,iBAAkB;UAAAkB,QAAA,EAAC;QAAM;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnDhF,OAAA,CAAClC,MAAM;UACL+I,OAAO,EAAE1D,YAAa;UACtBsB,OAAO,EAAC,WAAW;UACnBkD,QAAQ,EAAEtH,OAAQ;UAClB6D,EAAE,EAAE;YACFgC,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE;cACTA,eAAe,EAAE;YACnB;UACF,CAAE;UAAA9B,QAAA,GAEDpD,eAAe,GAAG,QAAQ,GAAG,QAAQ,EAAC,WACzC;QAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThF,OAAA,CAACxB,MAAM;MAACsC,IAAI,EAAEI,gBAAiB;MAACkG,OAAO,EAAEA,CAAA,KAAMjG,mBAAmB,CAAC,KAAK,CAAE;MAAAiD,QAAA,gBACxEpE,OAAA,CAACvB,WAAW;QAAA2F,QAAA,EAAC;MAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzChF,OAAA,CAACtB,aAAa;QAAA0F,QAAA,eACZpE,OAAA,CAACnC,UAAU;UAAAuG,QAAA,GAAC,oCACuB,EAAChD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEU,KAAK,EAAC,mCAC5D;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBhF,OAAA,CAACrB,aAAa;QAAAyF,QAAA,gBACZpE,OAAA,CAAClC,MAAM;UAAC+I,OAAO,EAAEA,CAAA,KAAM1F,mBAAmB,CAAC,KAAK,CAAE;UAAAiD,QAAA,EAAC;QAAM;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClEhF,OAAA,CAAClC,MAAM;UACL+I,OAAO,EAAE9C,mBAAoB;UAC7Ba,KAAK,EAAC,OAAO;UACbH,OAAO,EAAC,WAAW;UACnBkD,QAAQ,EAAEtH,OAAQ;UAAA+D,QAAA,EACnB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC9E,EAAA,CA/gBID,UAAoB;EAAA,QACPvC,WAAW,EAC8BC,WAAW,EAGlDA,WAAW,EACbA,WAAW;AAAA;AAAAiK,EAAA,GANxB3H,UAAoB;AAihB1B,eAAeA,UAAU;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}